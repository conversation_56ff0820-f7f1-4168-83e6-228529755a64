body {
    margin: 0;
    display: grid;
    place-items: center;
    background: black;
    height: 100vh;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

#minigame-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

svg {
    width: 90vmin;
    height: auto;
    cursor: default;
}

.light {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.drag {
    fill: white; 
    opacity: 0;
    cursor: grab;
}

.drag:active {
    cursor: grabbing;
}

.line {
    stroke-width: 18px;
    pointer-events: none;
}

.line-back {
    stroke-width: 30px;
    pointer-events: none;
}

.line-1 {
    stroke: #324d9c;
}

.line-1.line-back {
    stroke: #25378d;
}

.line-2 {
    stroke: #e52320;
}

.line-2.line-back {
    stroke: #a71916;
}

.line-3 {
    stroke: #ffeb13;
}

.line-3.line-back {
    stroke: #aa9f17;
}

.line-4 {
    stroke: #a6529a;
}

.line-4.line-back {
    stroke: #90378c;
}

/* Among Us style colors and styling */
.c{fill:#273065;stroke:#1a1b36}
.c,.d,.e,.f,.k,.u{stroke-miterlimit:10}
.c,.d,.e,.f,.u,.y{stroke-width:5px}
.d{fill:#71160e;stroke:#280f10}
.e{fill:#8c6c15}
.e,.u{stroke:#38321a}
.f{fill:#212021;stroke:#000}
.h{fill:#9b3015;stroke:#471d12}
.h,.y{stroke-linecap:round;stroke-linejoin:round}
.k,.y{fill:none}
.k{stroke:#1d1d1b;stroke-width:6px}
.l{fill:#d9c905}
.m{fill:#25378d}
.n{fill:#324d9c}
.o{fill:#a71916}
.p{fill:#e52320}
.q{fill:#aa9f17}
.r{fill:#ffeb13}
.s{fill:#90378c}
.t{fill:#a6529a}
.u{fill:#1d1d1b}
.v{fill:#5b5c64}
.w{fill:#292829}
.x{fill:#2f3038}
.y{stroke:#252526}

/* Animations */
@keyframes lightFlash {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

.light.flash {
    animation: lightFlash 0.5s ease-in-out;
}

/* Success animation */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.success-animation {
    animation: successPulse 0.5s ease-in-out;
}

/* Responsive design */
@media (max-width: 768px) {
    svg {
        width: 95vmin;
    }
}

@media (max-height: 600px) {
    svg {
        height: 90vh;
        width: auto;
    }
}
