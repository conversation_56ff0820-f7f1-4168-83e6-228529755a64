local isMinigameActive = false
local currentCallback = nil

-- Export function pro spuštění minihry
function startWireMinigame(callback)
    if isMinigameActive then
        return false
    end
    
    isMinigameActive = true
    currentCallback = callback
    
    -- <PERSON>tevři NUI
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "openMinigame"
    })
    
    return true
end

-- NUI Callbacks
RegisterNUICallback('minigameComplete', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(true)
        currentCallback = nil
    end
    
    -- <PERSON><PERSON><PERSON> notifikaci o úspěchu
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = true,
        args = {"[WIRE MINIGAME]", "Všechny drátky úspěšně spojeny! ✅"}
    })
    
    cb('ok')
end)

RegisterNUICallback('minigameFailed', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end
    
    -- <PERSON><PERSON><PERSON> notifika<PERSON> o neúspěchu
    TriggerEvent('chat:addMessage', {
        color = {255, 0, 0},
        multiline = true,
        args = {"[WIRE MINIGAME]", "Nějaký drát se pokazil! ❌"}
    })
    
    cb('ok')
end)

RegisterNUICallback('closeMinigame', function(data, cb)
    isMinigameActive = false
    SetNuiFocus(false, false)
    
    if currentCallback then
        currentCallback(false)
        currentCallback = nil
    end
    
    -- Pošli notifikaci o zavření
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = true,
        args = {"[WIRE MINIGAME]", "Minihra zavřena (ESC) ⚠️"}
    })
    
    cb('ok')
end)

-- Testovací commandy
RegisterCommand('testwire', function(source, args, rawCommand)
    if isMinigameActive then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[WIRE MINIGAME]", "Minihra už běží!"}
        })
        return
    end
    
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"[WIRE MINIGAME]", "Spouštím wire minihru..."}
    })
    
    startWireMinigame(function(success)
        if success then
            print("Wire minigame completed successfully!")
        else
            print("Wire minigame failed or was cancelled!")
        end
    end)
end, false)

RegisterCommand('testwire_export', function(source, args, rawCommand)
    if isMinigameActive then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[WIRE MINIGAME]", "Minihra už běží!"}
        })
        return
    end
    
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"[WIRE MINIGAME]", "Testování exportu..."}
    })
    
    -- Test exportu
    exports['valic_wire-minigame']:startWireMinigame(function(success)
        if success then
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                multiline = true,
                args = {"[EXPORT TEST]", "Export funguje! Minihra dokončena úspěšně! ✅"}
            })
        else
            TriggerEvent('chat:addMessage', {
                color = {255, 0, 0},
                multiline = true,
                args = {"[EXPORT TEST]", "Export funguje! Minihra selhala nebo byla zrušena! ❌"}
            })
        end
    end)
end, false)

-- Export
exports('startWireMinigame', startWireMinigame)

-- ESC key handler
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isMinigameActive then
            if IsControlJustPressed(0, 322) then -- ESC key
                SendNUIMessage({
                    type = "forceClose"
                })
            end
        end
    end
end)
