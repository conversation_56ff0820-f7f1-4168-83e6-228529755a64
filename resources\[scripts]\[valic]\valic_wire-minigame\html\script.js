console.clear();

let completedLights = [0, 0, 0, 0];
let isMinigameActive = false;
let draggableInstances = [];

// Správné pozice pro připojení drátů
const correctPositions = {
    1: {x: 670, y: 188},   // Modrý drát
    2: {x: 670, y: -188},  // Červený drát  
    3: {x: 670, y: 0},     // Žlutý drát
    4: {x: 670, y: 0}      // <PERSON>alový drát
};

// Audio element pro úspěch
let audioTask = null;

function initializeMinigame() {
    if (isMinigameActive) return;
    
    isMinigameActive = true;
    completedLights = [0, 0, 0, 0];
    
    // Vytvoř audio element
    audioTask = new Audio('sounds/task-complete.mp3');
    audioTask.volume = 0.5;
    
    // Inicializuj draggable elementy
    initializeDraggables();
    
    console.log('Wire minigame initialized');
}

function initializeDraggables() {
    // <PERSON><PERSON>č<PERSON>i př<PERSON>chozí instance
    draggableInstances.forEach(instance => {
        if (instance && instance.kill) {
            instance.kill();
        }
    });
    draggableInstances = [];
    
    // Drát 1 (modr<PERSON>)
    const drag1 = new Draggable('.drag-1', {
        onDrag: function () { 
            updateLine('.line-1', this.x + 120, this.y + 185); 
        },
        onRelease: function () {
            const correct = this.x === correctPositions[1].x && this.y === correctPositions[1].y;
            if (!correct) {
                reset('.drag-1', '.line-1', 70, 185);
                toggleLight(2, false);
            } else {
                toggleLight(2, true);
            }
        },
        liveSnap: {points: [correctPositions[1]], radius: 20}
    });
    draggableInstances.push(drag1);
    
    // Drát 2 (červený)
    const drag2 = new Draggable('.drag-2', {
        onDrag: function () { 
            updateLine('.line-2', this.x + 120, this.y + 375); 
        },
        onRelease: function () {
            const correct = this.x === correctPositions[2].x && this.y === correctPositions[2].y;
            if (!correct) {
                reset('.drag-2', '.line-2', 60, 375);
                toggleLight(1, false);
            } else {
                toggleLight(1, true);
            }
        },
        liveSnap: {points: [correctPositions[2]], radius: 20}
    });
    draggableInstances.push(drag2);
    
    // Drát 3 (žlutý)
    const drag3 = new Draggable('.drag-3', {
        onDrag: function () { 
            updateLine('.line-3', this.x + 120, this.y + 560); 
        },
        onRelease: function () {
            const correct = this.x === correctPositions[3].x && this.y === correctPositions[3].y;
            if (!correct) {
                reset('.drag-3', '.line-3', 60, 560);
                toggleLight(3, false);
            } else {
                toggleLight(3, true);
            }
        },
        liveSnap: {points: [correctPositions[3]], radius: 20}
    });
    draggableInstances.push(drag3);
    
    // Drát 4 (fialový)
    const drag4 = new Draggable('.drag-4', {
        onDrag: function () { 
            updateLine('.line-4', this.x + 120, this.y + 745); 
        },
        onRelease: function () {
            const correct = this.x === correctPositions[4].x && this.y === correctPositions[4].y;
            if (!correct) {
                reset('.drag-4', '.line-4', 60, 745);
                toggleLight(4, false);
            } else {
                toggleLight(4, true);
            }
        },
        liveSnap: {points: [correctPositions[4]], radius: 20}
    });
    draggableInstances.push(drag4);
}

function updateLine(selector, x, y) {
    gsap.set(selector, {
        attr: {
            x2: x,
            y2: y
        }
    });
}

function toggleLight(selector, visibility) {
    if (visibility) {
        completedLights[selector - 1] = 1;
        
        // Zkontroluj, jestli jsou všechny drátky připojené
        if (completedLights.every(light => light === 1)) {
            // Všechny drátky připojené - úspěch!
            handleMinigameSuccess();
        }
    } else {
        completedLights[selector - 1] = 0;
    }
    
    gsap.to(`.light-${selector}`, {
        opacity: visibility ? 1 : 0,
        duration: 0.3
    });
}

function handleMinigameSuccess() {
    console.log('All wires connected successfully!');
    
    // Přehraj zvuk úspěchu
    if (audioTask) {
        audioTask.play().catch(e => console.log('Audio play failed:', e));
    }
    
    // Animace úspěchu
    gsap.to('svg', {
        className: '+=success-animation',
        duration: 0.5
    });
    
    // Po 2 sekundách zavři minihru
    setTimeout(() => {
        closeMinigame(true);
    }, 2000);
}

function reset(drag, line, x, y) {
    gsap.to(drag, {
        duration: 0.3,
        ease: 'power2.out',
        x: 0,
        y: 0
    });
    gsap.to(line, {
        duration: 0.3,
        ease: 'power2.out',
        attr: {
            x2: x,
            y2: y
        }
    });
}

function closeMinigame(success = false) {
    isMinigameActive = false;
    
    // Vyčisti draggable instances
    draggableInstances.forEach(instance => {
        if (instance && instance.kill) {
            instance.kill();
        }
    });
    draggableInstances = [];
    
    // Skryj UI
    document.getElementById('minigame-container').style.display = 'none';
    
    // Resetuj všechno
    resetAllWires();
    
    // Pošli callback do FiveM
    if (success) {
        fetch(`https://${GetParentResourceName()}/minigameComplete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    } else {
        fetch(`https://${GetParentResourceName()}/minigameFailed`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    }
}

function resetAllWires() {
    completedLights = [0, 0, 0, 0];
    
    // Resetuj pozice drátů
    reset('.drag-1', '.line-1', 70, 185);
    reset('.drag-2', '.line-2', 60, 375);
    reset('.drag-3', '.line-3', 60, 560);
    reset('.drag-4', '.line-4', 60, 745);
    
    // Vypni všechna světla
    for (let i = 1; i <= 4; i++) {
        toggleLight(i, false);
    }
}

// Event listenery pro FiveM
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openMinigame':
            document.getElementById('minigame-container').style.display = 'flex';
            setTimeout(() => {
                initializeMinigame();
            }, 100);
            break;
            
        case 'forceClose':
            closeMinigame(false);
            break;
    }
});

// ESC key handler
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isMinigameActive) {
        fetch(`https://${GetParentResourceName()}/closeMinigame`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        });
    }
});

console.log('Wire minigame script loaded');
