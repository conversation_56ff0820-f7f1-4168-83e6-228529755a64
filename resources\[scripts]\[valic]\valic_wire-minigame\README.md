# Valic Wire Minigame

Wire minigame pro FiveM ve stylu Among Us, určen<PERSON> pro heisty a další aktivity.

## Instalace

1. Zkopíruj složku `valic_wire-minigame` do `resources/[scripts]/[valic]/`
2. <PERSON>řidej do `server.cfg`:
   ```
   ensure valic_wire-minigame
   ```
3. Restartuj server

## Použití

### Export pro jiné scripty
```lua
-- Spuštění minihry s callbackem
exports['valic_wire-minigame']:startWireMinigame(function(success)
    if success then
        print("Hráč úspěšně dokončil wire minigame!")
        -- Zde můžeš přidat logiku pro úspěšné dokončení
    else
        print("Hráč selhal nebo zrušil wire minigame!")
        -- Zde můžeš přidat logiku pro neúspěch
    end
end)
```

### Testovací commandy

#### `/testwire`
- Spustí wire minigame pro testování
- Dostupné pro všechny hráče
- Zobrazuje notifikace v chatu

#### `/testwire_export`
- Testuje export funkci
- Dostupné pro všechny hráče
- Zobrazuje výsledky exportu v chatu

#### `/wiretest_server`
- Server command pro testování
- Pouze pro adminy
- Spouští test ze serveru

## Ovládání

- **Drag & Drop**: Táhni drátky z levé strany na správné pozice na pravé straně
- **ESC**: Zavře minigame (počítá se jako neúspěch)
- **Auto-close**: Minihra se automaticky zavře po úspěšném dokončení

## Funkce

- ✅ Among Us vizuální styl
- ✅ Drag & drop interakce
- ✅ Vizuální feedback (světla)
- ✅ Zvukové efekty
- ✅ Auto-close po dokončení
- ✅ ESC pro zavření
- ✅ Export pro jiné scripty
- ✅ Notifikace o výsledku
- ✅ Testovací commandy

## Technické detaily

### Struktura souborů
```
valic_wire-minigame/
├── fxmanifest.lua
├── client/
│   └── client.lua
├── server/
│   └── server.lua
├── html/
│   ├── index.html
│   ├── style.css
│   ├── script.js
│   └── sounds/
│       └── task-complete.mp3
└── README.md
```

### Callbacks
- `minigameComplete`: Voláno při úspěšném dokončení
- `minigameFailed`: Voláno při neúspěchu
- `closeMinigame`: Voláno při zavření pomocí ESC

### Events
- `valic_wire:startTest`: Client event pro spuštění testu
- `valic_wire:requestMinigame`: Server event pro požadavek na minigame
- `valic_wire:logResult`: Server event pro logování výsledků

## Customizace

### Změna pozic drátů
V `html/script.js` uprav objekt `correctPositions`:
```javascript
const correctPositions = {
    1: {x: 670, y: 188},   // Modrý drát
    2: {x: 670, y: -188},  // Červený drát  
    3: {x: 670, y: 0},     // Žlutý drát
    4: {x: 670, y: 0}      // Fialový drát
};
```

### Změna barev drátů
V `html/style.css` uprav CSS třídy `.line-1` až `.line-4`

### Změna zvuku
Nahraď soubor `html/sounds/task-complete.mp3` vlastním zvukem

## Autor
Valic - Wire Minigame v1.0.0
