-- Server-side script pro wire minigame
print("^2[VALIC WIRE MINIGAME]^7 Server script loaded!")

-- Testovací server command
RegisterCommand('wiretest_server', function(source, args, rawCommand)
    local player = source
    if player == 0 then
        print("Wire minigame test command executed from server console")
        return
    end
    
    TriggerClientEvent('chat:addMessage', player, {
        color = {0, 255, 255},
        multiline = true,
        args = {"[SERVER TEST]", "Server command pro wire minigame test!"}
    })
    
    -- Trigger client event to start minigame
    TriggerClientEvent('valic_wire:startTest', player)
end, true) -- true = restricted to admins

-- Event pro spuštění minihry ze serveru
RegisterServerEvent('valic_wire:requestMinigame')
AddEventHandler('valic_wire:requestMinigame', function()
    local source = source
    TriggerClientEvent('valic_wire:startMinigame', source)
end)

-- Event pro logování výsledků
RegisterServerEvent('valic_wire:logResult')
AddEventHandler('valic_wire:logResult', function(success, playerId)
    local source = source
    local playerName = GetPlayerName(source)
    
    if success then
        print(string.format("^2[WIRE MINIGAME]^7 Player %s (%s) completed wire minigame successfully!", playerName, source))
    else
        print(string.format("^1[WIRE MINIGAME]^7 Player %s (%s) failed wire minigame!", playerName, source))
    end
end)
